// Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyBUWLGRTlpWYZZVwSZTtG3p7FIp-MGRtA0",
    authDomain: "coin-cf7eb.firebaseapp.com",
    databaseURL: "https://coin-cf7eb.firebaseio.com",
    projectId: "coin-cf7eb",
    storageBucket: "coin-cf7eb.firebasestorage.app",
    messagingSenderId: "509393422860",
    appId: "1:509393422860:web:5216337e75b939e4887c29"
};

// Instructions for setup:
// 1. Go to Firebase Console (https://console.firebase.google.com/)
// 2. Create a new project or select existing project
// 3. Go to Project Settings > General > Your apps
// 4. Add a web app and copy the configuration
// 5. Replace the values above with your actual configuration
// 6. Enable Authentication in Firebase Console
// 7. Enable Email/Password and Google sign-in methods
// 8. Enable Firestore Database
// 9. Set up Firestore security rules for your collections

window.firebaseConfig = firebaseConfig;
