# Complete Setup Guide

## Quick Start

1. **Install dependencies and run setup test:**
   ```bash
   npm run setup
   ```

2. **Configure Firebase and Cashfree (see detailed steps below)**

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Test the API endpoints:**
   ```bash
   npm test
   ```

## Detailed Setup Steps

### Step 1: Firebase Project Setup

1. **Create Firebase Project:**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Click "Create a project"
   - Enter project name and follow the setup wizard

2. **Enable Authentication:**
   - In Firebase Console, go to "Authentication"
   - Click "Get started"
   - Go to "Sign-in method" tab
   - Enable "Email/Password" and "Google" providers

3. **Enable Firestore Database:**
   - Go to "Firestore Database"
   - Click "Create database"
   - Choose "Start in test mode" for now
   - Select a location close to your users

4. **Get Web App Configuration:**
   - Go to Project Settings (gear icon)
   - Scroll down to "Your apps"
   - Click "Add app" and select web app
   - Register your app and copy the configuration

5. **Generate Admin SDK Key:**
   - Go to Project Settings > Service accounts
   - Click "Generate new private key"
   - Download the JSON file

### Step 2: Cashfree Account Setup

1. **Create Cashfree Account:**
   - Sign up at [Cashfree](https://www.cashfree.com/)
   - Complete KYC verification

2. **Get API Credentials:**
   - Go to Developers > API Keys
   - Copy App ID and Secret Key
   - For testing, use sandbox credentials

### Step 3: Environment Configuration

1. **Copy environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Fill in Firebase credentials in .env:**
   ```env
   FIREBASE_API_KEY=your_firebase_api_key
   FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   FIREBASE_PROJECT_ID=your_project_id
   FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   FIREBASE_APP_ID=your_app_id
   
   # From the downloaded JSON file:
   FIREBASE_ADMIN_PROJECT_ID=your_project_id
   FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
   FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
   ```

3. **Fill in Cashfree credentials:**
   ```env
   CASHFREE_APP_ID=your_cashfree_app_id
   CASHFREE_SECRET_KEY=your_cashfree_secret_key
   CASHFREE_BASE_URL=https://sandbox.cashfree.com/pg
   ```

4. **Update client-side Firebase config:**
   - Edit `public/firebase-config.js`
   - Replace the configuration with your Firebase web app config

### Step 4: Firestore Security Rules

Set up security rules in Firebase Console > Firestore Database > Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Orders are readable by the user who created them
    match /orders/{orderId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if false; // Only server can write orders
    }
  }
}
```

### Step 5: Testing

1. **Run setup verification:**
   ```bash
   npm run test-setup
   ```

2. **Start the server:**
   ```bash
   npm start
   ```

3. **Test API endpoints:**
   ```bash
   npm test
   ```

4. **Manual testing:**
   - Open http://localhost:3000
   - Try registering a new account
   - Test login functionality
   - Try subscribing to a plan

## Troubleshooting

### Common Issues

1. **Firebase connection errors:**
   - Check if all Firebase environment variables are set
   - Verify the private key format (should include \n for line breaks)
   - Ensure Firestore is enabled in Firebase Console

2. **Authentication errors:**
   - Verify Firebase Auth is enabled
   - Check if email/password provider is enabled
   - Ensure the domain is authorized in Firebase Auth settings

3. **Payment errors:**
   - Verify Cashfree credentials
   - Check if you're using the correct environment (sandbox vs production)
   - Ensure webhook URL is accessible

4. **CORS errors:**
   - Check if your domain is allowed in CORS settings
   - Verify Firebase Auth domain configuration

### Development Tips

1. **Use development mode:**
   ```bash
   npm run dev  # Uses nodemon for auto-restart
   ```

2. **Check logs:**
   - Server logs show in console
   - Browser console shows client-side errors
   - Firebase Console shows authentication logs

3. **Test with different browsers:**
   - Some browsers block third-party cookies
   - Test in incognito mode

## Production Deployment

1. **Environment variables:**
   - Set NODE_ENV=production
   - Use production Firebase project
   - Use production Cashfree credentials

2. **Security:**
   - Update Firestore security rules
   - Set proper CORS origins
   - Use HTTPS

3. **Monitoring:**
   - Set up error logging
   - Monitor payment webhooks
   - Track user authentication

## Support

- Check the main README.md for API documentation
- Review Firebase documentation for authentication issues
- Check Cashfree documentation for payment integration
- Create an issue in the repository for bugs
