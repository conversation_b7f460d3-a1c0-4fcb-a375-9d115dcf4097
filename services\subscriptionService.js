const { cfConfig, cfPaymentGateway, CFConfig, CFPaymentGateway, CFEnvironment } = require('../config/cashfree');
const { db, isFirebaseInitialized } = require('../config/firebase');
const { v4: uuidv4 } = require('uuid');

class SubscriptionService {
  constructor() {
    this.plans = {
      basic: {
        id: 'basic',
        name: 'Monthly Subscription',
        price: 299,
        duration: 30, // days
        features: ['Full API Access', 'Data Updates', 'Email Support', '30 Days Access']
      }
    };
  }

  // Get all available plans
  getPlans() {
    return this.plans;
  }

  // Create payment order with Cashfree
  async createPaymentOrder(userId, planId, userInfo) {
    try {
      console.log('Creating payment order - userId:', userId, 'planId:', planId);

      const plan = this.plans[planId];
      if (!plan) {
        throw new Error('Invalid plan selected');
      }

      console.log('Plan found:', plan);

      const orderId = `order_${Date.now()}_${uuidv4().substring(0, 8)}`;

      const orderRequest = {
        order_id: orderId,
        order_amount: plan.price,
        order_currency: 'INR',
        customer_details: {
          customer_id: userId,
          customer_name: userInfo.name || userInfo.email,
          customer_email: userInfo.email,
          customer_phone: userInfo.phone || '9999999999'
        },
        order_meta: {
          return_url: `${process.env.BASE_URL || 'http://localhost:3000'}/payment/success`,
          notify_url: `${process.env.BASE_URL || 'http://localhost:3000'}/webhook/cashfree`,
          payment_methods: 'cc,dc,nb,upi,wallet'
        },
        order_note: `Subscription for ${plan.name}`
      };

      console.log('Order request:', JSON.stringify(orderRequest, null, 2));
      console.log('Calling cfPaymentGateway.orderCreate...');

      // Use new SDK format
      const { CFOrderRequest, CFCustomerDetails } = require('cashfree-pg-sdk-nodejs');

      const customerDetails = new CFCustomerDetails();
      customerDetails.customerId = userId;
      customerDetails.customerPhone = userInfo.phone || '9999999999';
      customerDetails.customerEmail = userInfo.email;
      customerDetails.customerName = userInfo.name || userInfo.email;

      const cFOrderRequest = new CFOrderRequest();
      cFOrderRequest.orderId = orderId;
      cFOrderRequest.orderAmount = plan.price;
      cFOrderRequest.orderCurrency = 'INR';
      cFOrderRequest.customerDetails = customerDetails;
      cFOrderRequest.orderNote = `Subscription for ${plan.name}`;

      const response = await cfPaymentGateway.orderCreate(cfConfig, cFOrderRequest);

      console.log('Cashfree response:', response);
      
      // Store order details in Firestore (if Firebase is initialized)
      if (isFirebaseInitialized) {
        await db.collection('orders').doc(orderId).set({
          userId,
          planId,
          amount: plan.price,
          currency: 'INR',
          status: 'created',
          cashfreeOrderId: response.cfOrder.orderId,
          paymentSessionId: response.cfOrder.paymentSessionId,
          createdAt: new Date(),
          plan: plan
        });
      } else {
        console.warn('⚠️  Firebase not initialized - order details not stored in database');
      }

      return {
        orderId,
        paymentSessionId: response.cfOrder.paymentSessionId,
        payment_session_id: response.cfOrder.paymentSessionId, // For frontend compatibility
        orderAmount: plan.price,
        orderCurrency: 'INR'
      };
    } catch (error) {
      console.error('Error creating payment order:', error);
      throw new Error('Failed to create payment order');
    }
  }

  // Verify payment and activate subscription
  async verifyPaymentAndActivateSubscription(orderId) {
    try {
      // Get order details from Firestore
      const orderDoc = await db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw new Error('Order not found');
      }

      const orderData = orderDoc.data();
      
      // Verify payment with Cashfree using new SDK
      const paymentDetails = await cfPaymentGateway.getPaymentsForOrder(cfConfig, orderId);

      if (paymentDetails.cfPaymentsEntities && paymentDetails.cfPaymentsEntities.length > 0) {
        const payment = paymentDetails.cfPaymentsEntities[0];

        if (payment.paymentStatus === 'SUCCESS') {
          // Activate subscription
          await this.activateSubscription(orderData.userId, orderData.planId, orderData.plan);
          
          // Update order status
          await db.collection('orders').doc(orderId).update({
            status: 'completed',
            paymentId: payment.cfPaymentId,
            completedAt: new Date()
          });

          return { success: true, message: 'Subscription activated successfully' };
        } else {
          await db.collection('orders').doc(orderId).update({
            status: 'failed',
            failureReason: payment.paymentMessage || 'Payment failed'
          });
          
          return { success: false, message: 'Payment failed' };
        }
      }
      
      return { success: false, message: 'Payment verification failed' };
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw new Error('Payment verification failed');
    }
  }

  // Activate subscription for user
  async activateSubscription(userId, planId, plan) {
    try {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + plan.duration);

      const subscriptionData = {
        planId,
        planName: plan.name,
        active: true,
        startDate: new Date(),
        expiresAt,
        features: plan.features,
        price: plan.price
      };

      // Generate API key if user doesn't have one
      const userDoc = await db.collection('users').doc(userId).get();
      let apiKey;
      
      if (userDoc.exists && userDoc.data().apiKey) {
        apiKey = userDoc.data().apiKey;
      } else {
        apiKey = uuidv4();
      }

      await db.collection('users').doc(userId).set({
        subscription: subscriptionData,
        apiKey,
        updatedAt: new Date()
      }, { merge: true });

      return { apiKey, subscription: subscriptionData };
    } catch (error) {
      console.error('Error activating subscription:', error);
      throw new Error('Failed to activate subscription');
    }
  }

  // Get user subscription status
  async getSubscriptionStatus(userId) {
    try {
      // If Firebase is not initialized, return mock subscription for testing
      if (!isFirebaseInitialized) {
        console.warn('⚠️  Firebase not initialized - returning mock subscription status');
        return {
          active: false,
          message: 'No subscription found (Firebase not configured)',
          hasActiveSubscription: false
        };
      }

      const userDoc = await db.collection('users').doc(userId).get();

      if (!userDoc.exists) {
        return { active: false, message: 'User not found' };
      }

      const userData = userDoc.data();
      const subscription = userData.subscription;

      if (!subscription) {
        return { active: false, message: 'No subscription found' };
      }

      // Check if subscription has expired
      if (subscription.expiresAt && new Date(subscription.expiresAt.toDate()) < new Date()) {
        // Update subscription status
        await db.collection('users').doc(userId).update({
          'subscription.active': false
        });

        return {
          active: false,
          expired: true,
          expiresAt: subscription.expiresAt.toDate(),
          message: 'Subscription expired'
        };
      }

      return {
        active: subscription.active,
        plan: subscription.planName,
        expiresAt: subscription.expiresAt.toDate(),
        features: subscription.features,
        apiKey: userData.apiKey
      };
    } catch (error) {
      console.error('Error getting subscription status:', error);
      throw new Error('Failed to get subscription status');
    }
  }
}

module.exports = new SubscriptionService();
