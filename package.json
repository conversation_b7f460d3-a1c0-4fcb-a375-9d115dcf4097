{"name": "subscription-api", "version": "1.0.0", "description": "A subscription-based API service.", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-api.js", "test-setup": "node test-setup.js", "setup": "npm install && node test-setup.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^1.19.0", "cashfree-pg-sdk-nodejs": "^2.0.2", "dotenv": "^17.0.0", "express": "^4.17.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "lowdb": "^1.0.0", "node-fetch": "^2.6.1", "uuid": "^8.3.2"}, "devDependencies": {"nodemon": "^3.1.10"}}