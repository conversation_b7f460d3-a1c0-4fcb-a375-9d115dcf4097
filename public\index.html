<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Subscription Service</title>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .user-info {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .container {
            display: flex;
            gap: 40px;
            width: 100%;
            max-width: 1200px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .card {
            background-color: #ffffff;
            border: none;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            width: 100%;
            max-width: 350px;
            box-sizing: border-box;
            margin-bottom: 20px;
        }

        .auth-card {
            max-width: 400px;
        }

        h2 {
            color: #3498db;
            margin-top: 0;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        p {
            color: #555;
            line-height: 1.6;
        }

        input[type="email"], input[type="password"], input[type="text"] {
            width: calc(100% - 20px);
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
        }

        .action-button {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background-color 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }

        .action-button:hover {
            background-color: #2980b9;
        }

        .action-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .secondary-button {
            background-color: #95a5a6;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }

        .secondary-button:hover {
            background-color: #7f8c8d;
        }

        .plan-card {
            border: 2px solid #ecf0f1;
            transition: border-color 0.3s ease;
        }

        .plan-card:hover {
            border-color: #3498db;
        }

        .plan-price {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin: 15px 0;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .plan-features li {
            padding: 5px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .plan-features li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
        }

        .status-display {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }

        .success {
            background-color: #d5f4e6;
            border: 1px solid #27ae60;
            color: #27ae60;
        }

        .error {
            background-color: #fdf2f2;
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }

        .info {
            background-color: #ebf3fd;
            border: 1px solid #3498db;
            color: #2c3e50;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
        }

        .tab-button {
            flex: 1;
            padding: 10px;
            border: none;
            background: #ecf0f1;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
        }

        .tab-button.active {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>API Subscription Service</h1>
        <div id="user-info" class="user-info">
            <span id="user-email"></span>
            <button id="logout-btn" class="secondary-button">Logout</button>
        </div>
    </div>

    <!-- Authentication Section -->
    <div id="auth-section" class="container">
        <div class="card auth-card">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="showTab('login')">Login</button>
                <button class="tab-button" onclick="showTab('register')">Register</button>
            </div>

            <!-- Login Form -->
            <div id="login-tab">
                <h2>Login to Your Account</h2>
                <form id="login-form">
                    <input type="email" id="login-email" placeholder="Email Address" required>
                    <input type="password" id="login-password" placeholder="Password" required>
                    <button type="submit" class="action-button">Login</button>
                </form>
                <button id="google-login" class="action-button" style="background-color: #db4437;">
                    Login with Google
                </button>
            </div>

            <!-- Register Form -->
            <div id="register-tab" class="hidden">
                <h2>Create New Account</h2>
                <form id="register-form">
                    <input type="text" id="register-name" placeholder="Full Name" required>
                    <input type="email" id="register-email" placeholder="Email Address" required>
                    <input type="password" id="register-password" placeholder="Password (min 6 characters)" required>
                    <input type="text" id="register-phone" placeholder="Phone Number (optional)">
                    <button type="submit" class="action-button">Create Account</button>
                </form>
            </div>

            <div id="auth-status" class="status-display hidden"></div>
        </div>
    </div>

    <!-- Main Application Section -->
    <div id="app-section" class="container hidden">
        <!-- Subscription Plans -->
        <div class="card">
            <h2>Choose Your Plan</h2>
            <div id="subscription-status" class="status-display hidden"></div>
            <div id="plans-container"></div>
        </div>

        <!-- API Usage -->
        <div class="card">
            <h2>API Usage</h2>
            <p>Test your API access and view the data response.</p>
            <button id="fetch-data-button" class="action-button">Fetch Data</button>
            <div id="api-response" class="status-display hidden"></div>
        </div>

        <!-- Account Management -->
        <div class="card">
            <h2>Account Information</h2>
            <div id="account-info"></div>
            <button id="refresh-status" class="secondary-button">Refresh Status</button>
        </div>
    </div>

    <script>
        // Firebase Configuration (Replace with your actual config)
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "your-sender-id",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let userToken = null;

        // Auth state observer
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                currentUser = user;
                userToken = await user.getIdToken();
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
                await loadSubscriptionStatus();
                await loadPlans();
            } else {
                currentUser = null;
                userToken = null;
                showAuthSection();
            }
        });

        // Tab switching
        function showTab(tabName) {
            document.getElementById('login-tab').classList.toggle('hidden', tabName !== 'login');
            document.getElementById('register-tab').classList.toggle('hidden', tabName !== 'register');

            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Show/hide sections
        function showAuthSection() {
            document.getElementById('auth-section').classList.remove('hidden');
            document.getElementById('app-section').classList.add('hidden');
            document.getElementById('user-info').style.display = 'none';
        }

        function showAppSection() {
            document.getElementById('auth-section').classList.add('hidden');
            document.getElementById('app-section').classList.remove('hidden');
            document.getElementById('user-info').style.display = 'block';
        }

        function updateUserInfo(user) {
            document.getElementById('user-email').textContent = user.email;
        }

        // Authentication functions
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            try {
                showStatus('auth-status', 'Logging in...', 'info');
                await auth.signInWithEmailAndPassword(email, password);
                hideStatus('auth-status');
            } catch (error) {
                showStatus('auth-status', `Login failed: ${error.message}`, 'error');
            }
        });

        document.getElementById('register-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const phone = document.getElementById('register-phone').value;

            try {
                showStatus('auth-status', 'Creating account...', 'info');
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);

                // Update profile
                await userCredential.user.updateProfile({ displayName: name });

                // Create user profile on server
                const token = await userCredential.user.getIdToken();
                await fetch('/api/user/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ name, phone })
                });

                hideStatus('auth-status');
            } catch (error) {
                showStatus('auth-status', `Registration failed: ${error.message}`, 'error');
            }
        });

        document.getElementById('google-login').addEventListener('click', async () => {
            try {
                const provider = new firebase.auth.GoogleAuthProvider();
                await auth.signInWithPopup(provider);
            } catch (error) {
                showStatus('auth-status', `Google login failed: ${error.message}`, 'error');
            }
        });

        document.getElementById('logout-btn').addEventListener('click', () => {
            auth.signOut();
        });

        // Utility functions
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status-display ${type}`;
            element.classList.remove('hidden');
        }

        function hideStatus(elementId) {
            document.getElementById(elementId).classList.add('hidden');
        }

        // Load subscription plans
        async function loadPlans() {
            try {
                const response = await fetch('/api/plans');
                const plans = await response.json();

                const container = document.getElementById('plans-container');
                container.innerHTML = '';

                Object.values(plans).forEach(plan => {
                    const planDiv = document.createElement('div');
                    planDiv.className = 'plan-card card';
                    planDiv.innerHTML = `
                        <h3>${plan.name}</h3>
                        <div class="plan-price">₹${plan.price}/month</div>
                        <ul class="plan-features">
                            ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                        <button class="action-button" onclick="subscribeToPlan('${plan.id}')">
                            Subscribe Now
                        </button>
                    `;
                    container.appendChild(planDiv);
                });
            } catch (error) {
                console.error('Error loading plans:', error);
            }
        }

        // Subscribe to plan
        async function subscribeToPlan(planId) {
            try {
                if (!userToken) {
                    showStatus('subscription-status', 'Please login first', 'error');
                    return;
                }

                showStatus('subscription-status', 'Creating payment order...', 'info');

                const response = await fetch('/api/payment/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${userToken}`
                    },
                    body: JSON.stringify({ planId })
                });

                if (response.ok) {
                    const order = await response.json();

                    // Redirect to Cashfree payment page
                    const paymentUrl = `https://sandbox.cashfree.com/pg/view/checkout?order_id=${order.orderId}&order_token=${order.paymentSessionId}`;
                    window.open(paymentUrl, '_blank');

                    showStatus('subscription-status', 'Payment window opened. Complete payment to activate subscription.', 'info');
                } else {
                    const error = await response.text();
                    showStatus('subscription-status', `Payment creation failed: ${error}`, 'error');
                }
            } catch (error) {
                showStatus('subscription-status', `Error: ${error.message}`, 'error');
            }
        }

        // Load subscription status
        async function loadSubscriptionStatus() {
            try {
                if (!userToken) return;

                const response = await fetch('/api/subscription/status', {
                    headers: {
                        'Authorization': `Bearer ${userToken}`
                    }
                });

                if (response.ok) {
                    const status = await response.json();
                    updateAccountInfo(status);
                } else {
                    console.error('Failed to load subscription status');
                }
            } catch (error) {
                console.error('Error loading subscription status:', error);
            }
        }

        // Update account info display
        function updateAccountInfo(status) {
            const accountInfo = document.getElementById('account-info');

            if (status.active) {
                accountInfo.innerHTML = `
                    <div class="status-display success">
                        <strong>Active Subscription</strong><br>
                        Plan: ${status.plan}<br>
                        Expires: ${new Date(status.expiresAt).toLocaleDateString()}<br>
                        API Key: <code>${status.apiKey}</code>
                    </div>
                `;
            } else {
                accountInfo.innerHTML = `
                    <div class="status-display error">
                        <strong>No Active Subscription</strong><br>
                        ${status.expired ? 'Your subscription has expired.' : 'Please subscribe to a plan to access the API.'}
                    </div>
                `;
            }
        }

        // Fetch API data
        document.getElementById('fetch-data-button').addEventListener('click', async () => {
            try {
                if (!userToken) {
                    showStatus('api-response', 'Please login first', 'error');
                    return;
                }

                showStatus('api-response', 'Fetching data...', 'info');

                const response = await fetch('/api/data', {
                    headers: {
                        'Authorization': `Bearer ${userToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('api-response').innerHTML = `
                        <div class="status-display success">
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    showStatus('api-response', `API Error: ${error}`, 'error');
                }
            } catch (error) {
                showStatus('api-response', `Error: ${error.message}`, 'error');
            }
        });

        // Refresh status
        document.getElementById('refresh-status').addEventListener('click', () => {
            loadSubscriptionStatus();
        });
    </script>
</body>
</html>
