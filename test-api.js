// API endpoint testing script
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
    console.log('🧪 Testing API Endpoints...\n');

    // Test 1: Get subscription plans
    console.log('1. Testing GET /api/plans');
    try {
        const response = await fetch(`${BASE_URL}/api/plans`);
        if (response.ok) {
            const plans = await response.json();
            console.log('   ✅ Plans endpoint working');
            console.log(`   📊 Found ${Object.keys(plans).length} plans`);
        } else {
            console.log('   ❌ Plans endpoint failed:', response.status);
        }
    } catch (error) {
        console.log('   ❌ Plans endpoint error:', error.message);
    }

    // Test 2: Test authentication required endpoint
    console.log('\n2. Testing authentication protection');
    try {
        const response = await fetch(`${BASE_URL}/api/subscription/status`);
        if (response.status === 401) {
            console.log('   ✅ Authentication protection working');
        } else {
            console.log('   ❌ Authentication protection failed:', response.status);
        }
    } catch (error) {
        console.log('   ❌ Authentication test error:', error.message);
    }

    // Test 3: Test API data endpoint without auth
    console.log('\n3. Testing API data endpoint protection');
    try {
        const response = await fetch(`${BASE_URL}/api/data`);
        if (response.status === 401) {
            console.log('   ✅ API data endpoint protected');
        } else {
            console.log('   ❌ API data endpoint not protected:', response.status);
        }
    } catch (error) {
        console.log('   ❌ API data test error:', error.message);
    }

    // Test 4: Test webhook endpoint
    console.log('\n4. Testing webhook endpoint validation');
    try {
        const response = await fetch(`${BASE_URL}/webhook/cashfree`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
        if (response.status === 400) {
            console.log('   ✅ Webhook validation working');
        } else {
            console.log('   ❌ Webhook validation failed:', response.status);
        }
    } catch (error) {
        console.log('   ❌ Webhook test error:', error.message);
    }

    // Test 5: Test CORS headers
    console.log('\n5. Testing CORS headers');
    try {
        const response = await fetch(`${BASE_URL}/api/plans`);
        const corsHeader = response.headers.get('access-control-allow-origin');
        if (corsHeader) {
            console.log('   ✅ CORS headers present');
        } else {
            console.log('   ❌ CORS headers missing');
        }
    } catch (error) {
        console.log('   ❌ CORS test error:', error.message);
    }

    console.log('\n🎯 API Testing Complete!');
}

// Check if server is running
async function checkServer() {
    try {
        const response = await fetch(`${BASE_URL}/api/plans`);
        return true;
    } catch (error) {
        return false;
    }
}

// Main execution
async function main() {
    const serverRunning = await checkServer();
    
    if (!serverRunning) {
        console.log('❌ Server is not running on http://localhost:3000');
        console.log('Please start the server with "npm start" first');
        return;
    }

    await testAPI();
}

main().catch(console.error);
