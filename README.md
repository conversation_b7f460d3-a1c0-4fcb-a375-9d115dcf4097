# API Subscription Service with Firebase Auth & Cashfree Payments

A modern subscription-based API service built with Firebase Authentication and Cashfree payment integration.

## Features

- 🔐 **Firebase Authentication** - Email/password and Google sign-in
- 💳 **Cashfree Payment Integration** - Production-ready payment processing (₹299/month)
- 📊 **Monthly Subscription** - 30 days of full API access
- 🎯 **User Dashboard** - Complete dashboard with:
  - Days remaining counter with progress bar
  - Profile management (name, email, phone)
  - Password change functionality
  - API key management (copy/regenerate)
  - Live API testing interface
- 🔑 **API Key Management** - Automatic API key generation
- 📱 **Responsive UI** - Modern, mobile-friendly interface
- 🛡️ **Secure API Endpoints** - Token-based authentication

## Tech Stack

- **Backend**: Node.js, Express.js
- **Authentication**: Firebase Auth
- **Database**: Firebase Firestore
- **Payments**: Cashfree Payment Gateway
- **Frontend**: Vanilla JavaScript, HTML5, CSS3

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd rttt
npm install
```

### 2. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication:
   - Go to Authentication > Sign-in method
   - Enable Email/Password and Google
4. Enable Firestore Database:
   - Go to Firestore Database
   - Create database in test mode
5. Get your configuration:
   - Go to Project Settings > General
   - Add a web app and copy the config
6. Generate Admin SDK key:
   - Go to Project Settings > Service accounts
   - Generate new private key (JSON file)

### 3. Cashfree Setup

1. Sign up at [Cashfree](https://www.cashfree.com/)
2. Get your App ID and Secret Key from dashboard
3. For testing, use sandbox credentials

### 4. Environment Configuration

1. Copy `.env.example` to `.env`
2. Fill in your Firebase and Cashfree credentials:

```env
# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Firebase Admin SDK
FIREBASE_ADMIN_PROJECT_ID=your_project_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com

# Cashfree Configuration
CASHFREE_APP_ID=your_cashfree_app_id
CASHFREE_SECRET_KEY=your_cashfree_secret_key
CASHFREE_BASE_URL=https://sandbox.cashfree.com/pg

# Application
PORT=3000
NODE_ENV=development
```

3. Update `public/firebase-config.js` with your Firebase client configuration

### 5. Firestore Security Rules

Set up Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Orders are readable by the user who created them
    match /orders/{orderId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## Running the Application

```bash
npm start
```

The application will be available at `http://localhost:3000`

## API Endpoints

### Authentication Required Endpoints

- `GET /api/plans` - Get subscription plans
- `POST /api/user/profile` - Create/update user profile
- `POST /api/payment/create-order` - Create payment order
- `GET /api/subscription/status` - Get subscription status
- `GET /api/data` - Access API data (requires active subscription)

### Webhook Endpoints

- `POST /webhook/cashfree` - Cashfree payment webhook
- `GET /payment/success` - Payment success page

### Legacy Endpoints

- `GET /api/data-legacy` - API data access with API key (backward compatibility)

## Subscription Plans

- **Basic Plan**: ₹199/month - API Access, Basic Support
- **Premium Plan**: ₹499/month - API Access, Priority Support, Advanced Analytics
- **Enterprise Plan**: ₹999/month - API Access, 24/7 Support, Custom Integration

## Usage Flow

1. **User Registration/Login**: Users sign up or log in using Firebase Auth
2. **Plan Selection**: Users choose a subscription plan
3. **Payment**: Secure payment processing via Cashfree
4. **API Access**: Upon successful payment, users get API access
5. **Data Fetching**: Users can fetch data using their authenticated session

## Security Features

- Firebase token verification for all API endpoints
- Subscription status validation
- Secure payment processing
- API key-based access for legacy support
- CORS protection
- Input validation

## Development

### Project Structure

```
├── config/
│   ├── firebase.js      # Firebase Admin configuration
│   └── cashfree.js      # Cashfree configuration
├── middleware/
│   └── auth.js          # Authentication middleware
├── services/
│   └── subscriptionService.js  # Subscription management
├── public/
│   ├── index.html       # Frontend application
│   └── firebase-config.js  # Client-side Firebase config
├── server.js            # Main server file
└── .env                 # Environment variables
```

### Testing

Test the payment flow in Cashfree sandbox mode before going live.

## Deployment

1. Set `NODE_ENV=production` in your environment
2. Update Cashfree base URL to production endpoint
3. Configure proper Firestore security rules
4. Set up proper CORS policies
5. Use HTTPS in production

## Support

For issues and questions, please check the documentation or create an issue in the repository.
