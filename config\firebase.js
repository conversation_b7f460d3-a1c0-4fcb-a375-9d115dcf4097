const admin = require('firebase-admin');
require('dotenv').config();

// Firebase Admin SDK configuration
let isFirebaseInitialized = false;

try {
  // Check if we have a valid private key
  if (process.env.FIREBASE_ADMIN_PRIVATE_KEY &&
      process.env.FIREBASE_ADMIN_PRIVATE_KEY !== "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_FROM_SERVICE_ACCOUNT_JSON_FILE\n-----END PRIVATE KEY-----\n") {

    const serviceAccount = {
      type: process.env.FIREBASE_ADMIN_TYPE,
      project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
      private_key_id: process.env.FIREBASE_ADMIN_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_ADMIN_CLIENT_ID,
      auth_uri: process.env.FIREBASE_ADMIN_AUTH_URI,
      token_uri: process.env.FIREBASE_ADMIN_TOKEN_URI,
      auth_provider_x509_cert_url: process.env.FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL,
      client_x509_cert_url: process.env.FIREBASE_ADMIN_CLIENT_X509_CERT_URL
    };

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
      });
    }

    isFirebaseInitialized = true;
    console.log('✅ Firebase Admin initialized successfully');
  } else {
    console.warn('⚠️  Firebase Admin not initialized - missing service account key');
    console.warn('📖 Please follow FIREBASE_SETUP.md to configure Firebase Admin');
  }
} catch (error) {
  console.error('❌ Firebase Admin initialization failed:', error.message);
  console.warn('🔄 Server will continue without Firebase Admin features');
}

// Export Firebase services (with fallbacks if not initialized)
let db, auth;

if (isFirebaseInitialized) {
  db = admin.firestore();
  auth = admin.auth();
} else {
  // Create mock objects to prevent crashes
  db = {
    collection: () => ({
      doc: () => ({
        get: () => Promise.resolve({ exists: false }),
        set: () => Promise.resolve(),
        update: () => Promise.resolve()
      })
    })
  };
  auth = {
    verifyIdToken: () => Promise.reject(new Error('Firebase Admin not initialized'))
  };
}

module.exports = {
  admin,
  db,
  auth,
  isFirebaseInitialized
};
