const { auth, db } = require('../config/firebase');

// Middleware to verify Firebase token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.split(' ')[1];
    const decodedToken = await auth.verifyIdToken(token);
    
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Middleware to check subscription status
const checkSubscription = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const userDoc = await db.collection('users').doc(req.user.uid).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    const subscription = userData.subscription;

    if (!subscription || !subscription.active) {
      return res.status(403).json({ error: 'No active subscription' });
    }

    // Check if subscription has expired
    if (subscription.expiresAt && new Date(subscription.expiresAt.toDate()) < new Date()) {
      // Update subscription status to inactive
      await db.collection('users').doc(req.user.uid).update({
        'subscription.active': false
      });
      
      return res.status(403).json({ error: 'Subscription expired' });
    }

    req.subscription = subscription;
    next();
  } catch (error) {
    console.error('Subscription check error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Middleware to verify API key (for backward compatibility)
const verifyApiKey = async (req, res, next) => {
  try {
    const apiKey = req.query.api_key || req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({ error: 'API key is required' });
    }

    // Query Firestore for user with this API key
    const usersRef = db.collection('users');
    const snapshot = await usersRef.where('apiKey', '==', apiKey).get();
    
    if (snapshot.empty) {
      return res.status(403).json({ error: 'Invalid API key' });
    }

    const userDoc = snapshot.docs[0];
    const userData = userDoc.data();
    
    // Check subscription status
    if (!userData.subscription || !userData.subscription.active) {
      return res.status(403).json({ error: 'No active subscription' });
    }

    // Check if subscription has expired
    if (userData.subscription.expiresAt && 
        new Date(userData.subscription.expiresAt.toDate()) < new Date()) {
      return res.status(403).json({ error: 'Subscription expired' });
    }

    req.user = { uid: userDoc.id, ...userData };
    req.subscription = userData.subscription;
    next();
  } catch (error) {
    console.error('API key verification error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  verifyToken,
  checkSubscription,
  verifyApiKey
};
