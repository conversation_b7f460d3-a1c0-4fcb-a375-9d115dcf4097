# Firebase Service Account Setup

## Quick Setup Steps

### 1. Get Firebase Service Account Key

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: `coin-cf7eb`
3. **Go to Project Settings** (gear icon in sidebar)
4. **Click on "Service accounts" tab**
5. **Click "Generate new private key"**
6. **Download the JSON file**

### 2. Update .env File

Open the downloaded JSON file and copy the values to your `.env` file:

```json
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### 3. Update These Values in .env:

```env
FIREBASE_ADMIN_PRIVATE_KEY_ID=abc123...
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_CLIENT_ID=*********...
FIREBASE_ADMIN_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40coin-cf7eb.iam.gserviceaccount.com
```

### 4. Enable Required Services

In Firebase Console:

1. **Authentication**:
   - Go to Authentication > Sign-in method
   - Enable "Email/Password"
   - Enable "Google" (optional)

2. **Firestore Database**:
   - Go to Firestore Database
   - Create database in test mode
   - Choose a location close to your users

3. **Set Firestore Rules**:
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /users/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       match /orders/{orderId} {
         allow read: if request.auth != null && request.auth.uid == resource.data.userId;
         allow write: if false;
       }
     }
   }
   ```

### 5. Test the Setup

```bash
npm run test-setup
```

If everything is configured correctly, you should see:
- ✅ Firebase configuration: Working
- ✅ Cashfree configuration: Working

### 6. Start the Application

```bash
npm start
```

Then visit: http://localhost:3000

## Important Notes

- **Keep your service account key secure** - never commit it to version control
- **The private key must include the \n characters** for line breaks
- **Use double quotes** around the private key in the .env file
- **Your Cashfree credentials are already configured** for production use

## Troubleshooting

### Firebase Auth Error
- Make sure Authentication is enabled in Firebase Console
- Check that Email/Password provider is enabled

### Firestore Permission Error
- Update Firestore security rules as shown above
- Make sure Firestore is enabled

### Private Key Error
- Ensure the private key includes \n for line breaks
- Use double quotes around the entire private key
- Copy the exact value from the downloaded JSON file

## Ready to Use Features

Once setup is complete, your users can:

1. **Register/Login** at http://localhost:3000
2. **Access Dashboard** at http://localhost:3000/dashboard.html
3. **Subscribe for ₹299/month** for 30 days access
4. **Manage their profile** and change password
5. **Get API keys** and test the API
6. **Track subscription days** remaining

The system is production-ready with your Cashfree credentials!
