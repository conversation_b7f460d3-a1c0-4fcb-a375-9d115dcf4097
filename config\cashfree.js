const { CFConfig, CFPaymentGateway, CFEnvironment } = require('cashfree-pg-sdk-nodejs');
require('dotenv').config();

console.log('Initializing Cashfree with:');
console.log('App ID:', process.env.CASHFREE_APP_ID);
console.log('Environment: PRODUCTION (forced)');

let cfConfig, cfPaymentGateway;

try {
  // Initialize Cashfree with new SDK - Force production since you have production credentials
  cfConfig = new CFConfig(
    CFEnvironment.PRODUCTION,
    "2022-09-01",
    process.env.CASHFREE_APP_ID,
    process.env.CASHFREE_SECRET_KEY
  );

  cfPaymentGateway = new CFPaymentGateway();

  console.log('✅ Cashfree initialized successfully');
} catch (error) {
  console.error('❌ Cashfree initialization failed:', error.message);
}

module.exports = {
  cfConfig,
  cfPaymentGateway,
  CFConfig,
  CFPaymentGateway,
  CFEnvironment
};
