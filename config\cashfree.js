const { Cashfree } = require('cashfree-pg-sdk-nodejs');
require('dotenv').config();

// Initialize Cashfree
Cashfree.XClientId = process.env.CASHFREE_APP_ID;
Cashfree.XClientSecret = process.env.CASHFREE_SECRET_KEY;
Cashfree.XEnvironment = process.env.NODE_ENV === 'production' 
  ? Cashfree.Environment.PRODUCTION 
  : Cashfree.Environment.SANDBOX;

module.exports = {
  Cashfree,
  baseUrl: process.env.CASHFREE_BASE_URL
};
