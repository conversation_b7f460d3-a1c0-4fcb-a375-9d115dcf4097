// Input validation middleware

const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

const validatePhone = (phone) => {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone);
};

const validatePlanId = (planId) => {
    const validPlans = ['basic'];
    return validPlans.includes(planId);
};

// Middleware to validate user profile data
const validateUserProfile = (req, res, next) => {
    const { name, phone } = req.body;
    
    const errors = [];
    
    if (!name || typeof name !== 'string' || name.trim().length < 2) {
        errors.push('Name must be at least 2 characters long');
    }
    
    if (phone && !validatePhone(phone)) {
        errors.push('Invalid phone number format');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({ error: 'Validation failed', details: errors });
    }
    
    // Sanitize inputs
    req.body.name = name.trim();
    if (phone) req.body.phone = phone.trim();
    
    next();
};

// Middleware to validate payment order data
const validatePaymentOrder = (req, res, next) => {
    const { planId } = req.body;
    
    if (!planId || !validatePlanId(planId)) {
        return res.status(400).json({ error: 'Invalid plan ID' });
    }
    
    next();
};

// Middleware to validate webhook data
const validateWebhookData = (req, res, next) => {
    const { orderId, orderStatus } = req.body;
    
    if (!orderId || typeof orderId !== 'string') {
        return res.status(400).json({ error: 'Invalid order ID' });
    }
    
    if (!orderStatus || typeof orderStatus !== 'string') {
        return res.status(400).json({ error: 'Invalid order status' });
    }
    
    next();
};

// General error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);
    
    // Firebase Auth errors
    if (err.code && err.code.startsWith('auth/')) {
        return res.status(401).json({ error: 'Authentication failed', details: err.message });
    }
    
    // Firestore errors
    if (err.code && err.code.includes('firestore')) {
        return res.status(500).json({ error: 'Database error', details: 'Please try again later' });
    }
    
    // Cashfree errors
    if (err.message && err.message.includes('cashfree')) {
        return res.status(500).json({ error: 'Payment processing error', details: 'Please try again later' });
    }
    
    // Default error
    res.status(500).json({ 
        error: 'Internal server error', 
        details: process.env.NODE_ENV === 'development' ? err.message : 'Please try again later'
    });
};

// Request logging middleware
const requestLogger = (req, res, next) => {
    const timestamp = new Date().toISOString();
    const method = req.method;
    const url = req.url;
    const ip = req.ip || req.connection?.remoteAddress || 'unknown';
    
    console.log(`[${timestamp}] ${method} ${url} - IP: ${ip}`);
    
    next();
};

module.exports = {
    validateUserProfile,
    validatePaymentOrder,
    validateWebhookData,
    errorHandler,
    requestLogger
};
