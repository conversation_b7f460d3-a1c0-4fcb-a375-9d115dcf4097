<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - API Subscription Service</title>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .user-info h1 {
            color: #2c3e50;
            margin-bottom: 5px;
            font-weight: 700;
        }

        .user-email {
            color: #7f8c8d;
            font-size: 14px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .subscription-status {
            text-align: center;
            padding: 20px;
        }

        .status-active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-inactive {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .days-left {
            font-size: 48px;
            font-weight: 700;
            margin: 15px 0;
        }

        .days-label {
            font-size: 16px;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .api-key-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .api-key {
            font-family: 'Courier New', monospace;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 12px;
            border-radius: 6px;
            word-break: break-all;
            margin: 10px 0;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #3498db;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .alert-success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .alert-error {
            background: #fdf2f2;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .alert-info {
            background: #ebf3fd;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .header-actions {
                justify-content: center;
            }
        }

        .icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <div class="user-info">
                <h1 id="userName">Welcome!</h1>
                <div class="user-email" id="userEmail">Loading...</div>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="showChangePasswordModal()">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                    </svg>
                    Change Password
                </button>
                <button class="btn btn-secondary" onclick="logout()">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/>
                    </svg>
                    Logout
                </button>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Subscription Status Card -->
            <div class="card">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"/>
                    </svg>
                    Subscription Status
                </h2>
                <div class="subscription-status">
                    <div id="subscriptionStatus" class="status-inactive">
                        <div>No Active Subscription</div>
                    </div>
                    <div id="daysLeftContainer" class="hidden">
                        <div class="days-left" id="daysLeft">0</div>
                        <div class="days-label">Days Remaining</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div id="expiryDate"></div>
                    </div>
                    <button id="subscribeBtn" class="btn btn-primary" onclick="subscribe()">
                        Subscribe Now - ₹299/month
                    </button>
                </div>
            </div>

            <!-- API Access Card -->
            <div class="card">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M7,7H17V10H7V7M7,13H17V16H7V13M7,19H17V22H7V19M3,7H5V10H3V7M3,13H5V16H3V13M3,19H5V22H3V19Z"/>
                    </svg>
                    API Access
                </h2>
                <div class="api-key-section">
                    <label>Your API Key:</label>
                    <div class="api-key" id="apiKeyDisplay">
                        <span id="apiKeyText">Loading...</span>
                        <button class="copy-btn" onclick="copyApiKey()">Copy</button>
                    </div>
                    <button class="btn btn-secondary" onclick="regenerateApiKey()">
                        Regenerate API Key
                    </button>
                </div>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="testApi()">Test API</button>
                    <div id="apiTestResult" class="hidden"></div>
                </div>
            </div>

            <!-- Account Settings Card -->
            <div class="card">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                    Account Settings
                </h2>
                <form id="profileForm">
                    <div class="form-group">
                        <label for="displayName">Display Name:</label>
                        <input type="text" id="displayName" class="form-control" placeholder="Enter your name">
                    </div>
                    <div class="form-group">
                        <label for="phoneNumber">Phone Number:</label>
                        <input type="tel" id="phoneNumber" class="form-control" placeholder="Enter your phone number">
                    </div>
                    <button type="submit" class="btn btn-primary">Update Profile</button>
                </form>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alertContainer"></div>
    </div>

    <!-- Change Password Modal -->
    <div id="changePasswordModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeChangePasswordModal()">&times;</span>
            <h2>Change Password</h2>
            <form id="changePasswordForm">
                <div class="form-group">
                    <label for="currentPassword">Current Password:</label>
                    <input type="password" id="currentPassword" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">New Password:</label>
                    <input type="password" id="newPassword" class="form-control" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm New Password:</label>
                    <input type="password" id="confirmPassword" class="form-control" required>
                </div>
                <button type="submit" class="btn btn-primary">Change Password</button>
            </form>
        </div>
    </div>

    <script>
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let userApiKey = null;

        // Check authentication state
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                currentUser = user;
                await loadUserData();
                await loadSubscriptionStatus();
            } else {
                // Redirect to login if not authenticated
                window.location.href = 'index.html';
            }
        });

        // Load user data
        async function loadUserData() {
            try {
                document.getElementById('userName').textContent = `Welcome, ${currentUser.displayName || 'User'}!`;
                document.getElementById('userEmail').textContent = currentUser.email;

                // Load user profile from Firestore
                const userDoc = await db.collection('users').doc(currentUser.uid).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    document.getElementById('displayName').value = userData.name || currentUser.displayName || '';
                    document.getElementById('phoneNumber').value = userData.phone || '';
                    userApiKey = userData.apiKey;

                    if (userApiKey) {
                        document.getElementById('apiKeyText').textContent = userApiKey;
                    } else {
                        await generateApiKey();
                    }
                }
            } catch (error) {
                console.error('Error loading user data:', error);
                showAlert('Error loading user data', 'error');
            }
        }

        // Load subscription status
        async function loadSubscriptionStatus() {
            try {
                const token = await currentUser.getIdToken();
                const response = await fetch('/api/subscription/status', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateSubscriptionDisplay(data);
                } else {
                    updateSubscriptionDisplay({ hasActiveSubscription: false });
                }
            } catch (error) {
                console.error('Error loading subscription status:', error);
                updateSubscriptionDisplay({ hasActiveSubscription: false });
            }
        }

        // Update subscription display
        function updateSubscriptionDisplay(subscriptionData) {
            const statusElement = document.getElementById('subscriptionStatus');
            const daysLeftContainer = document.getElementById('daysLeftContainer');
            const subscribeBtn = document.getElementById('subscribeBtn');

            if (subscriptionData.hasActiveSubscription) {
                const expiryDate = new Date(subscriptionData.expiryDate);
                const now = new Date();
                const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                const totalDays = 30;
                const progress = Math.max(0, (daysLeft / totalDays) * 100);

                statusElement.className = 'status-active';
                statusElement.innerHTML = '<div>Active Subscription</div>';

                document.getElementById('daysLeft').textContent = Math.max(0, daysLeft);
                document.getElementById('expiryDate').textContent = `Expires: ${expiryDate.toLocaleDateString()}`;
                document.getElementById('progressFill').style.width = `${progress}%`;

                daysLeftContainer.classList.remove('hidden');
                subscribeBtn.textContent = 'Extend Subscription - ₹299/month';
                subscribeBtn.style.background = '#27ae60';
            } else {
                statusElement.className = 'status-inactive';
                statusElement.innerHTML = '<div>No Active Subscription</div>';

                daysLeftContainer.classList.add('hidden');
                subscribeBtn.textContent = 'Subscribe Now - ₹299/month';
                subscribeBtn.style.background = '#3498db';
            }
        }

        // Generate API key
        async function generateApiKey() {
            try {
                const token = await currentUser.getIdToken();
                const response = await fetch('/api/user/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: currentUser.displayName || 'User',
                        phone: document.getElementById('phoneNumber').value || ''
                    })
                });

                if (response.ok) {
                    await loadUserData(); // Reload to get the API key
                    showAlert('API key generated successfully', 'success');
                } else {
                    throw new Error('Failed to generate API key');
                }
            } catch (error) {
                console.error('Error generating API key:', error);
                showAlert('Error generating API key', 'error');
            }
        }

        // Subscribe function
        async function subscribe() {
            try {
                const token = await currentUser.getIdToken();
                const response = await fetch('/api/payment/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        planId: 'basic'
                    })
                });

                if (response.ok) {
                    const orderData = await response.json();

                    // Redirect to Cashfree payment page
                    if (orderData.payment_session_id) {
                        const cashfree = new Cashfree({
                            mode: "production" // Using production mode with your credentials
                        });

                        cashfree.checkout({
                            paymentSessionId: orderData.payment_session_id,
                            redirectTarget: "_self"
                        });
                    } else {
                        throw new Error('Payment session not created');
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to create payment order');
                }
            } catch (error) {
                console.error('Error creating subscription:', error);
                showAlert('Error creating subscription: ' + error.message, 'error');
            }
        }

        // Copy API key
        function copyApiKey() {
            const apiKeyText = document.getElementById('apiKeyText').textContent;
            navigator.clipboard.writeText(apiKeyText).then(() => {
                showAlert('API key copied to clipboard', 'success');
            }).catch(() => {
                showAlert('Failed to copy API key', 'error');
            });
        }

        // Regenerate API key
        async function regenerateApiKey() {
            if (confirm('Are you sure you want to regenerate your API key? The old key will stop working.')) {
                await generateApiKey();
            }
        }

        // Test API
        async function testApi() {
            try {
                const resultDiv = document.getElementById('apiTestResult');
                resultDiv.innerHTML = '<div class="alert alert-info">Testing API...</div>';
                resultDiv.classList.remove('hidden');

                const response = await fetch(`/api/data-legacy?apiKey=${userApiKey}`);

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>API Test Successful!</strong><br>
                            Status: ${data.status}<br>
                            Data points: ${data.data ? data.data.length : 0}
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('apiTestResult').innerHTML = `
                    <div class="alert alert-error">
                        <strong>API Test Failed:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Profile form submission
        document.getElementById('profileForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                const token = await currentUser.getIdToken();
                const response = await fetch('/api/user/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: document.getElementById('displayName').value,
                        phone: document.getElementById('phoneNumber').value
                    })
                });

                if (response.ok) {
                    showAlert('Profile updated successfully', 'success');

                    // Update display name in Firebase Auth
                    await currentUser.updateProfile({
                        displayName: document.getElementById('displayName').value
                    });

                    document.getElementById('userName').textContent = `Welcome, ${document.getElementById('displayName').value}!`;
                } else {
                    throw new Error('Failed to update profile');
                }
            } catch (error) {
                console.error('Error updating profile:', error);
                showAlert('Error updating profile', 'error');
            }
        });

        // Change password modal functions
        function showChangePasswordModal() {
            document.getElementById('changePasswordModal').style.display = 'block';
        }

        function closeChangePasswordModal() {
            document.getElementById('changePasswordModal').style.display = 'none';
            document.getElementById('changePasswordForm').reset();
        }

        // Change password form submission
        document.getElementById('changePasswordForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword !== confirmPassword) {
                showAlert('Passwords do not match', 'error');
                return;
            }

            try {
                await currentUser.updatePassword(newPassword);
                showAlert('Password changed successfully', 'success');
                closeChangePasswordModal();
            } catch (error) {
                console.error('Error changing password:', error);
                if (error.code === 'auth/requires-recent-login') {
                    showAlert('Please log out and log back in before changing your password', 'error');
                } else {
                    showAlert('Error changing password: ' + error.message, 'error');
                }
            }
        });

        // Logout function
        function logout() {
            auth.signOut().then(() => {
                window.location.href = 'index.html';
            });
        }

        // Show alert function
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;

            alertContainer.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('changePasswordModal');
            if (event.target === modal) {
                closeChangePasswordModal();
            }
        }
    </script>

    <!-- Cashfree SDK -->
    <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
</body>
</html>
