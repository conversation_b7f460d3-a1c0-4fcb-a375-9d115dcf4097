// Test script to verify the setup
require('dotenv').config();

console.log('🔍 Testing API Subscription Service Setup...\n');

// Test 1: Environment Variables
console.log('1. Checking Environment Variables:');
const requiredEnvVars = [
    'FIREBASE_API_KEY',
    'FIREBASE_AUTH_DOMAIN',
    'FIREBASE_PROJECT_ID',
    'CASHFREE_APP_ID',
    'CASHFREE_SECRET_KEY'
];

let envVarsOk = true;
requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
        console.log(`   ✅ ${varName}: Set`);
    } else {
        console.log(`   ❌ ${varName}: Missing`);
        envVarsOk = false;
    }
});

if (!envVarsOk) {
    console.log('\n❌ Please set up your environment variables in .env file');
    console.log('   Copy .env.example to .env and fill in your credentials\n');
}

// Test 2: Firebase Configuration
console.log('\n2. Testing Firebase Configuration:');
try {
    const { db, auth } = require('./config/firebase');
    console.log('   ✅ Firebase Admin SDK initialized');
    
    // Test Firestore connection
    db.collection('test').limit(1).get()
        .then(() => {
            console.log('   ✅ Firestore connection successful');
        })
        .catch(error => {
            console.log('   ❌ Firestore connection failed:', error.message);
        });
} catch (error) {
    console.log('   ❌ Firebase configuration error:', error.message);
}

// Test 3: Cashfree Configuration
console.log('\n3. Testing Cashfree Configuration:');
try {
    const { Cashfree } = require('./config/cashfree');
    console.log('   ✅ Cashfree SDK initialized');
    console.log(`   📍 Environment: ${Cashfree.XEnvironment}`);
} catch (error) {
    console.log('   ❌ Cashfree configuration error:', error.message);
}

// Test 4: Dependencies
console.log('\n4. Checking Dependencies:');
const dependencies = [
    'express',
    'firebase-admin',
    'cashfree-pg-sdk-nodejs',
    'dotenv',
    'bcryptjs'
];

dependencies.forEach(dep => {
    try {
        require(dep);
        console.log(`   ✅ ${dep}: Installed`);
    } catch (error) {
        console.log(`   ❌ ${dep}: Missing - run 'npm install ${dep}'`);
    }
});

// Test 5: File Structure
console.log('\n5. Checking File Structure:');
const fs = require('fs');
const requiredFiles = [
    'config/firebase.js',
    'config/cashfree.js',
    'middleware/auth.js',
    'middleware/validation.js',
    'services/subscriptionService.js',
    'public/index.html',
    'public/firebase-config.js',
    '.env.example'
];

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}: Exists`);
    } else {
        console.log(`   ❌ ${file}: Missing`);
    }
});

// Test 6: Server Start Test
console.log('\n6. Testing Server Startup:');
try {
    const express = require('express');
    const app = express();
    
    // Test basic middleware
    app.use(express.json());
    
    const server = app.listen(0, () => {
        const port = server.address().port;
        console.log(`   ✅ Server can start on port ${port}`);
        server.close();
    });
} catch (error) {
    console.log('   ❌ Server startup error:', error.message);
}

console.log('\n🎯 Setup Test Complete!');
console.log('\nNext Steps:');
console.log('1. Set up your Firebase project and update .env file');
console.log('2. Set up your Cashfree account and update .env file');
console.log('3. Update public/firebase-config.js with your Firebase client config');
console.log('4. Run "npm start" to start the server');
console.log('5. Visit http://localhost:3000 to test the application');

console.log('\n📚 Documentation:');
console.log('- README.md: Complete setup instructions');
console.log('- .env.example: Environment variables template');
console.log('- Firebase Console: https://console.firebase.google.com/');
console.log('- Cashfree Dashboard: https://www.cashfree.com/');
