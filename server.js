const express = require('express');
const bodyParser = require('body-parser');
const { v4: uuidv4 } = require('uuid');
const fetch = require('node-fetch');
require('dotenv').config();

// Firebase and services
const { db, auth, isFirebaseInitialized } = require('./config/firebase');
const subscriptionService = require('./services/subscriptionService');
const { verifyToken, checkSubscription, verifyApiKey } = require('./middleware/auth');
const {
    validateUserProfile,
    validatePaymentOrder,
    validateWebhookData,
    errorHandler,
    requestLogger
} = require('./middleware/validation');

const app = express();
const port = process.env.PORT || 3000;

// Security middleware
app.use((req, res, next) => {
    // CORS headers
    res.header('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key');

    // Security headers
    res.header('X-Content-Type-Options', 'nosniff');
    res.header('X-Frame-Options', 'DENY');
    res.header('X-XSS-Protection', '1; mode=block');
    res.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');

    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Rate limiting middleware (basic implementation)
const requestCounts = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100; // max requests per window

app.use((req, res, next) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    const now = Date.now();

    if (!requestCounts.has(clientIp)) {
        requestCounts.set(clientIp, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    } else {
        const clientData = requestCounts.get(clientIp);

        if (now > clientData.resetTime) {
            clientData.count = 1;
            clientData.resetTime = now + RATE_LIMIT_WINDOW;
        } else {
            clientData.count++;

            if (clientData.count > RATE_LIMIT_MAX_REQUESTS) {
                return res.status(429).json({
                    error: 'Too many requests. Please try again later.'
                });
            }
        }
    }

    next();
});

// Add request logging
app.use(requestLogger);

app.use(express.static('public'));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Get subscription plans
app.get('/api/plans', (req, res) => {
    try {
        const plans = subscriptionService.getPlans();
        res.json(plans);
    } catch (error) {
        console.error('Error fetching plans:', error);
        res.status(500).json({ error: 'Failed to fetch plans' });
    }
});

// Test Cashfree configuration (for debugging)
app.get('/api/test/cashfree', async (req, res) => {
    try {
        const { cfConfig, cfPaymentGateway } = require('./config/cashfree');
        const { CFOrderRequest, CFCustomerDetails } = require('cashfree-pg-sdk-nodejs');

        // Test basic Cashfree configuration
        const orderId = `test_${Date.now()}`;

        const customerDetails = new CFCustomerDetails();
        customerDetails.customerId = 'test_user';
        customerDetails.customerPhone = '9999999999';
        customerDetails.customerEmail = '<EMAIL>';
        customerDetails.customerName = 'Test User';

        const cFOrderRequest = new CFOrderRequest();
        cFOrderRequest.orderId = orderId;
        cFOrderRequest.orderAmount = 1;
        cFOrderRequest.orderCurrency = 'INR';
        cFOrderRequest.customerDetails = customerDetails;
        cFOrderRequest.orderNote = 'Test order';

        console.log('Testing Cashfree with order:', cFOrderRequest);
        const response = await cfPaymentGateway.orderCreate(cfConfig, cFOrderRequest);

        res.json({
            success: true,
            message: 'Cashfree is working',
            orderId: orderId,
            response: response.cfOrder
        });
    } catch (error) {
        console.error('Cashfree test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.response?.data || 'No additional details'
        });
    }
});

// Test payment order creation without authentication (for debugging)
app.post('/api/test/payment-order', async (req, res) => {
    try {
        const { planId = 'basic', userEmail = '<EMAIL>', userName = 'Test User' } = req.body;

        const userInfo = {
            email: userEmail,
            name: userName,
            phone: '9999999999'
        };

        const order = await subscriptionService.createPaymentOrder('test_user_123', planId, userInfo);
        res.json({
            success: true,
            message: 'Payment order created successfully',
            order
        });
    } catch (error) {
        console.error('Test payment order error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.stack
        });
    }
});

// Create user profile after Firebase authentication
app.post('/api/user/profile', verifyToken, validateUserProfile, async (req, res) => {
    try {
        const { name, phone } = req.body;
        const userId = req.user.uid;

        await db.collection('users').doc(userId).set({
            email: req.user.email,
            name: name || req.user.name,
            phone,
            createdAt: new Date(),
            updatedAt: new Date()
        }, { merge: true });

        res.json({ success: true, message: 'Profile created successfully' });
    } catch (error) {
        console.error('Error creating profile:', error);
        res.status(500).json({ error: 'Failed to create profile' });
    }
});

// Create payment order
app.post('/api/payment/create-order', verifyToken, validatePaymentOrder, async (req, res) => {
    try {
        console.log('Creating payment order for user:', req.user.uid);
        console.log('Plan ID:', req.body.planId);

        const { planId } = req.body;
        const userId = req.user.uid;

        const userInfo = {
            email: req.user.email,
            name: req.user.name || req.user.displayName || 'User',
            phone: req.body.phone || '9999999999'
        };

        console.log('User info:', userInfo);

        const order = await subscriptionService.createPaymentOrder(userId, planId, userInfo);
        console.log('Order created successfully:', order);

        res.json(order);
    } catch (error) {
        console.error('Error creating payment order:', error);
        res.status(500).json({ error: error.message });
    }
});

// Verify payment and activate subscription
app.post('/api/payment/verify', async (req, res) => {
    try {
        const { orderId } = req.body;
        const result = await subscriptionService.verifyPaymentAndActivateSubscription(orderId);
        res.json(result);
    } catch (error) {
        console.error('Error verifying payment:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get subscription status
app.get('/api/subscription/status', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const status = await subscriptionService.getSubscriptionStatus(userId);
        res.json(status);
    } catch (error) {
        console.error('Error getting subscription status:', error);
        res.status(500).json({ error: error.message });
    }
});

// API data endpoint with Firebase token authentication
app.get('/api/data', verifyToken, checkSubscription, async (req, res) => {
    try {
        const ts = Date.now();
        const url = `https://draw.ar-lottery01.com/WinGo/WinGo_30S/GetHistoryIssuePage.json?ts=${ts}`;

        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Accept": "application/json"
            }
        });

        if (!response.ok) throw new Error(`Status: ${response.status}`);

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error("Fetch error:", error);
        res.status(500).json({ error: `Error fetching data: ${error.message}` });
    }
});

// API data endpoint with API key (for backward compatibility)
app.get('/api/data-legacy', verifyApiKey, async (req, res) => {
    try {
        const ts = Date.now();
        const url = `https://draw.ar-lottery01.com/WinGo/WinGo_30S/GetHistoryIssuePage.json?ts=${ts}`;

        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Accept": "application/json"
            }
        });

        if (!response.ok) throw new Error(`Status: ${response.status}`);

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error("Fetch error:", error);
        res.status(500).json({ error: `Error fetching data: ${error.message}` });
    }
});

// Cashfree webhook endpoint
app.post('/webhook/cashfree', validateWebhookData, async (req, res) => {
    try {
        const { orderId, orderStatus } = req.body;

        if (orderStatus === 'PAID') {
            await subscriptionService.verifyPaymentAndActivateSubscription(orderId);
        }

        res.status(200).json({ success: true });
    } catch (error) {
        console.error('Webhook error:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});

// Payment success page
app.get('/payment/success', (req, res) => {
    res.send(`
        <html>
            <head><title>Payment Success</title></head>
            <body>
                <h1>Payment Successful!</h1>
                <p>Your subscription has been activated. You can now close this window and return to the application.</p>
                <script>
                    setTimeout(() => {
                        window.close();
                    }, 3000);
                </script>
            </body>
        </html>
    `);
});

// Error handling middleware (must be last)
app.use(errorHandler);

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});
