# Firebase Configuration
FIREBASE_API_KEY=AIzaSyBUWLGRTlpWYZZVwSZTtG3p7FIp-MGRtA0
FIREBASE_AUTH_DOMAIN=coin-cf7eb.firebaseapp.com
FIREBASE_PROJECT_ID=coin-cf7eb
FIREBASE_STORAGE_BUCKET=coin-cf7eb.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:5216337e75b939e4887c29

# Firebase Admin SDK (for server-side)
FIREBASE_ADMIN_TYPE=service_account
FIREBASE_ADMIN_PROJECT_ID=coin-cf7eb
FIREBASE_ADMIN_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
FIREBASE_ADMIN_CLIENT_ID=your_client_id
FIREBASE_ADMIN_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_ADMIN_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_ADMIN_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your_project.iam.gserviceaccount.com

# Cashfree Configuration (Production)
CASHFREE_APP_ID=80890d10637137583d840feb209808
CASHFREE_SECRET_KEY=cfsk_ma_prod_8471b75e8a7ddbb99b7eecfe317ac2ba_80d13666
CASHFREE_BASE_URL=https://api.cashfree.com/pg

# Application Configuration
PORT=3000
NODE_ENV=development

# Subscription Plans
MONTHLY_PLAN_PRICE=299
